import React, { useState, useRef } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  GestureResponderEvent,
} from 'react-native';
import { useTheme } from '../theme';

interface EmojiBarProps {
  onEmojiClick: (emoji: string) => void;
  onEmojiLongPress?: (emoji: string) => void;
  onEmojiRelease?: () => void;
}

const EmojiBar: React.FC<EmojiBarProps> = ({
  onEmojiClick,
  onEmojiLongPress,
  onEmojiRelease
}) => {
  const { colors } = useTheme();

  // Define emoji mapping with mood names (matching frontend)
  const emojis = [
    { text: '😊', mood: 'happy' },
    { text: '😂', mood: 'laughing' },
    { text: '😡', mood: 'angry' },
    { text: '😢', mood: 'sad' },
    { text: '❤️', mood: 'love' },
    { text: '👍', mood: 'thumbsUp' }
  ];

  // Long press handling
  const [activeEmojiIndex, setActiveEmojiIndex] = useState<number | null>(null);
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const longPressDuration = 500; // ms

  // Handle touch start for long press
  const handlePressIn = (emoji: string, index: number) => {
    if (!onEmojiLongPress) return;

    longPressTimerRef.current = setTimeout(() => {
      setActiveEmojiIndex(index);
      onEmojiLongPress(emoji);
    }, longPressDuration);
  };

  // Handle touch end
  const handlePressOut = (emoji: string) => {
    // Clear the long press timer
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    if (activeEmojiIndex !== null) {
      // This was a long press, handle release
      setActiveEmojiIndex(null);
      onEmojiRelease?.();

      // Small delay to reset state
      setTimeout(() => {
        setActiveEmojiIndex(null);
      }, 50);
    } else {
      // This was a regular tap
      onEmojiClick(emoji);
    }
  };

  // Handle press cancel (when user drags finger away)
  const handlePressCancel = () => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }

    if (activeEmojiIndex !== null) {
      setActiveEmojiIndex(null);
      onEmojiRelease?.();

      setTimeout(() => {
        setActiveEmojiIndex(null);
      }, 50);
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.emojiBar}>
      {emojis.map((emoji, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.emojiButton,
            activeEmojiIndex === index && styles.longPressActive
          ]}
          onPressIn={() => handlePressIn(emoji.text, index)}
          onPressOut={() => handlePressOut(emoji.text)}
          onPress={() => {}} // Empty onPress to prevent double firing
          delayPressIn={0}
          delayPressOut={0}
          activeOpacity={0.7}
          accessibilityLabel={`Insert ${emoji.text} emoji`}
        >
          <Text style={styles.emojiText}>{emoji.text}</Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  emojiBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: colors.cardBackground,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  emojiButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  longPressActive: {
    backgroundColor: colors.gray100,
    transform: [{ scale: 1.1 }],
  },
  emojiText: {
    fontSize: 24,
  },
});

export default EmojiBar;
